@echo off
:: 设置代码页为UTF-8以支持中文显示
chcp 65001 >nul 2>&1
title Lanhai Product Analyzer v3.0 - PaddleOCR 3.1

echo.
echo ========================================
echo   Lanhai Product Analyzer v3.0 Launcher
echo   PaddleOCR 3.1 - Digital Recognition Fixed
echo ========================================
echo.

:: 切换到脚本所在目录 (支持跨盘符)
cd /d "%~dp0"

:: 检查并激活lanhai虚拟环境
echo [INFO] Checking lanhai virtual environment...

:: 尝试多个可能的环境路径
set "LANHAI_FOUND=0"
set "PYTHON_EXE="

:: 路径1: C:\Users\<USER>\lanhai (用户指定的路径)
if exist "C:\Users\<USER>\lanhai\Scripts\python.exe" (
    echo [SUCCESS] Found lanhai environment: C:\Users\<USER>\lanhai
    set "PYTHON_EXE=C:\Users\<USER>\lanhai\Scripts\python.exe"
    set "PIP_EXE=C:\Users\<USER>\lanhai\Scripts\pip.exe"
    if exist "C:\Users\<USER>\lanhai\Scripts\activate.bat" (
        call "C:\Users\<USER>\lanhai\Scripts\activate.bat"
    )
    set "LANHAI_FOUND=1"
    goto :env_activated
)

:: 路径2: 当前用户目录下的lanhai
if exist "%USERPROFILE%\lanhai\Scripts\python.exe" (
    echo [SUCCESS] Found lanhai environment: %USERPROFILE%\lanhai
    set "PYTHON_EXE=%USERPROFILE%\lanhai\Scripts\python.exe"
    set "PIP_EXE=%USERPROFILE%\lanhai\Scripts\pip.exe"
    if exist "%USERPROFILE%\lanhai\Scripts\activate.bat" (
        call "%USERPROFILE%\lanhai\Scripts\activate.bat"
    )
    set "LANHAI_FOUND=1"
    goto :env_activated
)

:: 路径3: 项目目录下的lanhai
if exist "%~dp0lanhai\Scripts\python.exe" (
    echo [SUCCESS] Found lanhai environment: %~dp0lanhai
    set "PYTHON_EXE=%~dp0lanhai\Scripts\python.exe"
    set "PIP_EXE=%~dp0lanhai\Scripts\pip.exe"
    if exist "%~dp0lanhai\Scripts\activate.bat" (
        call "%~dp0lanhai\Scripts\activate.bat"
    )
    set "LANHAI_FOUND=1"
    goto :env_activated
)

:: 路径4: conda环境
where conda >nul 2>&1
if %errorlevel% == 0 (
    echo [INFO] Trying to activate conda lanhai environment...
    call conda activate lanhai >nul 2>&1
    if %errorlevel% == 0 (
        echo [SUCCESS] conda lanhai environment activated
        set "PYTHON_EXE=python"
        set "PIP_EXE=pip"
        set "LANHAI_FOUND=1"
        goto :env_activated
    )
)

:env_not_found
echo [WARNING] lanhai environment not found, using system Python
echo [TIP] Recommend creating lanhai environment for best experience:
echo    conda create -n lanhai python=3.10
echo    conda activate lanhai
echo    pip install paddleocr==3.1.0 pandas openpyxl pillow
set "PYTHON_EXE=python"
set "PIP_EXE=pip"
goto :continue

:env_activated
echo [SUCCESS] lanhai environment activated
echo [INFO] PaddleOCR 3.1 ready

:continue

echo.
echo [INFO] Starting Lanhai Product Analyzer...
echo.

:: 检查Python和依赖
echo [INFO] Checking Python environment...
"%PYTHON_EXE%" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found! Please ensure Python is correctly installed
    echo [TIP] Please visit https://python.org to download and install Python
    goto :error_exit
)

echo [INFO] Checking PaddleOCR version...
"%PYTHON_EXE%" -c "import paddleocr; print('[SUCCESS] PaddleOCR version:', paddleocr.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] PaddleOCR not installed or incorrect version
    echo [INFO] Trying to install PaddleOCR 3.1.0...
    "%PIP_EXE%" install paddleocr==3.1.0 --quiet
    if %errorlevel% neq 0 (
        echo [ERROR] PaddleOCR installation failed
        echo [TIP] Please run manually: pip install paddleocr==3.1.0
        goto :error_exit
    )
    echo [SUCCESS] PaddleOCR 3.1.0 installed successfully
)

echo.
echo [INFO] Starting Lanhai Product Analyzer v3.0...
echo [INFO] Digital recognition issues perfectly solved!
echo.

:: 运行主程序
"%PYTHON_EXE%" main.py

:: 检查程序退出状态
if %errorlevel% == 0 (
    echo.
    echo ========================================
    echo   [SUCCESS] Program exited normally
    echo   [THANKS] Thank you for using Lanhai Product Analyzer v3.0
    echo ========================================
) else (
    echo.
    echo ========================================
    echo   [WARNING] Program exited abnormally (Error code: %errorlevel%)
    echo   [TIP] Please check error messages or contact technical support
    echo ========================================
)

echo.
echo [TIP] Virtual environment will exit automatically when window closes
echo Press any key to exit...
pause >nul
goto :end

:error_exit
echo.
echo ========================================
echo   [ERROR] Startup failed
echo   [TIP] Please check environment configuration and retry
echo ========================================
echo.
echo Press any key to exit...
pause >nul

:end
